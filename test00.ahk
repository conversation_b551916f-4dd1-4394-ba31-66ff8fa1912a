#Requires AutoHotkey v2.0
#Include UIA-v2-1.1.0\Lib\UIA.ahk

global windows := [], scanResults := []

; 测试UIA是否正常工作
try {
    testElement := UIA.GetRootElement()
    if !testElement {
        MsgBox("警告：UIA库可能未正确加载或初始化失败！")
    }
} catch as e {
    MsgBox("错误：UIA库加载失败！`n错误信息：" . e.message)
}

CreateMainGUI()

CreateMainGUI() {
    global listBox, infoText, editSearch, textStat, windows
    winGui := Gui("+Resize", "UIA 控件结构检查器")

    winGui.AddText("x10 y10 w300 h20", "选择目标窗口：")
    listBox := winGui.AddListBox("x10 y30 w300 h100 vlistBox")

    btnRefresh := winGui.AddButton("x320 y30 w100 h30", "刷新窗口")
    btnSimple := winGui.AddButton("x430 y30 w100 h30", "简单模式")
    btnScan := winGui.AddButton("x320 y70 w100 h30", "扫描结构")
    btnExport := winGui.AddButton("x320 y110 w100 h30", "导出结果")

    winGui.AddText("x10 y140 w300 h20", "搜索关键词（Name 或 Type）：")
    editSearch := winGui.AddEdit("x10 y160 w300 h30")
    btnSearch := winGui.AddButton("x320 y160 w100 h30", "搜索")

    winGui.AddText("x10 y200 w420 h20", "控件信息/搜索结果：")
    infoText := winGui.AddEdit("x10 y220 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnStat := winGui.AddButton("x10 y380 w200 h30", "类型统计")
    textStat := winGui.AddEdit("x10 y420 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnMonitor := winGui.AddButton("x220 y380 w100 h30", "悬停监控")
    btnStopMonitor := winGui.AddButton("x330 y380 w100 h30", "停止监控")

    btnRefresh.OnEvent("Click", RefreshWindowList)
    btnSimple.OnEvent("Click", SimpleWindowList)
    btnScan.OnEvent("Click", ScanUIA)
    btnExport.OnEvent("Click", ExportResult)
    btnSearch.OnEvent("Click", SearchControls)
    btnStat.OnEvent("Click", ShowControlTypeStat)
    btnMonitor.OnEvent("Click", StartMouseMonitor)
    btnStopMonitor.OnEvent("Click", StopMouseMonitor)

    RefreshWindowList()
    winGui.Show("w550 h600")
}

RefreshWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; 添加调试信息
    windowCount := 0
    addedCount := 0
    debugInfo := "窗口扫描详情:`n`n"

    ; Get all window handles using AutoHotkey's WinGetList
    windowList := WinGetList()

    for hwnd in windowList {
        windowCount++
        try {
            title := WinGetTitle(hwnd)
            className := WinGetClass(hwnd)
            processName := WinGetProcessName(hwnd)

            ; 记录所有窗口信息用于调试
            debugInfo .= "窗口 " . windowCount . ": " . title . " [" . className . "] (" . processName . ")`n"

            ; 更宽松的过滤条件
            if (title != "" && title != "Program Manager" && title != "Desktop") ||
               (className != "" && !InStr(className, "Shell_TrayWnd")) {

                ; 创建显示标题
                displayTitle := title
                if displayTitle == "" {
                    displayTitle := "[" . className . "] - " . processName
                }

                ; 先尝试创建UIA元素，如果失败则使用基本信息
                winElement := ""
                try {
                    winElement := UIA.ElementFromHandle(hwnd)
                } catch {
                    ; 如果UIA失败，仍然添加窗口但标记为基本模式
                    winElement := "BasicMode"
                }

                windows.Push({Title: displayTitle, Win: winElement, Hwnd: hwnd, ClassName: className, ProcessName: processName})
                listBox.Add(displayTitle)
                addedCount++
            }
        } catch as e {
            debugInfo .= "窗口 " . windowCount . ": [错误] " . e.message . "`n"
            continue
        }
    }

    ; 显示调试信息
    MsgBox("扫描完成！`n总窗口数: " . windowCount . "`n可用窗口数: " . addedCount . "`n`n" . debugInfo)
}

SimpleWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; 简单模式：不使用UIA，只获取基本窗口信息
    windowCount := 0
    addedCount := 0

    ; Get all window handles using AutoHotkey's WinGetList
    windowList := WinGetList()

    for hwnd in windowList {
        windowCount++
        try {
            title := WinGetTitle(hwnd)
            className := WinGetClass(hwnd)
            processName := WinGetProcessName(hwnd)

            ; 非常宽松的过滤条件，几乎显示所有窗口
            if title != "" || (className != "" && processName != "") {

                ; 创建显示标题
                displayTitle := title
                if displayTitle == "" {
                    displayTitle := "[" . className . "] - " . processName
                }

                ; 不使用UIA，直接添加基本信息
                windows.Push({Title: displayTitle, Win: "SimpleMode", Hwnd: hwnd, ClassName: className, ProcessName: processName})
                listBox.Add(displayTitle)
                addedCount++
            }
        } catch {
            continue
        }
    }

    MsgBox("简单模式扫描完成！`n总窗口数: " . windowCount . "`n添加窗口数: " . addedCount . "`n`n注意：此模式下某些功能可能受限")
}

ScanUIA(*) {
    global listBox, windows, scanResults, infoText
    selected := listBox.Value
    if !selected {
        MsgBox("请选择一个窗口")
        return
    }

    scanResults := []
    win := windows[selected].Win

    ; 检查是否为简单模式或基本模式
    if win == "SimpleMode" || win == "BasicMode" {
        MsgBox("此窗口使用简单模式添加，无法进行UIA扫描。`n请使用'刷新窗口'按钮重新获取窗口列表，或尝试鼠标悬停监控功能。")
        return
    }

    try {
        root := win.Element
        TraverseElement(root)
        info := ""
        for ctrl in scanResults {
            info .= ctrl.Path " | " ctrl.Type " | " ctrl.Name "`n"
        }
        infoText.Value := info != "" ? info : "未找到任何控件"
    } catch as e {
        MsgBox("扫描失败：" . e.message . "`n`n可能原因：`n1. 窗口已关闭`n2. UIA访问被拒绝`n3. 窗口不支持UIA`n`n建议使用鼠标悬停监控功能。")
        infoText.Value := "扫描失败，请尝试其他窗口或使用鼠标悬停监控"
    }
}

TraverseElement(element, path := "") {
    global scanResults
    name := element.Name
    type := element.ControlType
    fullPath := path "/" type (name ? " [" name "]" : "")
    scanResults.Push({Path: fullPath, Type: type, Name: name, Element: element})
    for child in element.FindAllChildren() {
        TraverseElement(child, fullPath)
    }
}

ExportResult(*) {
    global scanResults
    if scanResults.Length = 0 {
        MsgBox("请先进行结构扫描")
        return
    }
    FileSelect := FileSelect("S", , "保存为...", "Text Files (*.txt)")
    if FileSelect {
        txt := ""
        for item in scanResults {
            txt .= item.Path " | " item.Type " | " item.Name "`n"
        }
        FileAppend(txt, FileSelect, "UTF-8")
        MsgBox("导出完成")
    }
}

SearchControls(*) {
    global scanResults, editSearch, infoText
    keyword := editSearch.Value
    if keyword = "" {
        MsgBox("请输入关键词")
        return
    }
    matches := ""
    for item in scanResults {
        if InStr(item.Name, keyword) || InStr(item.Type, keyword)
            matches .= item.Path " | " item.Type " | " item.Name "`n"
    }
    infoText.Value := matches != "" ? matches : "未找到匹配控件"
}

StartMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 200)
    MsgBox("鼠标悬停监控已启动（每 200ms 更新一次）`n移动鼠标到任意控件上查看信息")
}

StopMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 0)
    MsgBox("鼠标悬停监控已停止")
}

WatchUnderMouse() {
    global infoText
    try {
        ; 获取鼠标位置
        MouseGetPos(&x, &y, &hwnd)

        ; 尝试使用UIA获取元素信息
        try {
            el := UIA.ElementFromPoint()
            if el {
                info := "=== UIA 控件信息 ===`n"
                info .= "Name: " . (el.Name ? el.Name : "[无名称]") . "`n"
                info .= "Type: " . (el.ControlType ? el.ControlType : "[未知类型]") . "`n"
                info .= "Value: " . (el.Value ? el.Value : "[无值]") . "`n"
                info .= "AutomationId: " . (el.AutomationId ? el.AutomationId : "[无ID]") . "`n"
                info .= "ClassName: " . (el.ClassName ? el.ClassName : "[无类名]") . "`n"
                info .= "`n=== 窗口信息 ===`n"
                info .= "窗口句柄: " . hwnd . "`n"
                info .= "窗口标题: " . WinGetTitle(hwnd) . "`n"
                info .= "窗口类名: " . WinGetClass(hwnd) . "`n"
                info .= "鼠标位置: (" . x . ", " . y . ")"
                infoText.Value := info
            } else {
                infoText.Value := "无法获取UIA元素信息`n窗口句柄: " . hwnd . "`n鼠标位置: (" . x . ", " . y . ")"
            }
        } catch as e {
            ; UIA失败时显示基本窗口信息
            info := "=== 基本窗口信息 ===`n"
            info .= "窗口句柄: " . hwnd . "`n"
            info .= "窗口标题: " . WinGetTitle(hwnd) . "`n"
            info .= "窗口类名: " . WinGetClass(hwnd) . "`n"
            info .= "进程名: " . WinGetProcessName(hwnd) . "`n"
            info .= "鼠标位置: (" . x . ", " . y . ")`n"
            info .= "`nUIA错误: " . e.message
            infoText.Value := info
        }
    } catch as e {
        infoText.Value := "监控错误: " . e.message
    }
}

ShowControlTypeStat(*) {
    global scanResults, textStat
    stat := Map()
    for item in scanResults {
        type := item.Type
        stat[type] := stat.Has(type) ? stat[type] + 1 : 1
    }
    list := []
    for k, v in stat {
        list.Push({Type: k, Count: v})
    }
    SortArrayByCount(list)
    output := ""
    for item in list {
        output .= item.Type ": " item.Count "`n"
    }
    textStat.Value := output
}

SortArrayByCount(arr) {
    if !arr || arr.Length <= 1
        return arr
    n := arr.Length
    Loop n - 1 {
        i := A_Index
        Loop n - i {
            j := A_Index
            if (j + 1) > n
                continue
            a := arr[j]
            b := arr[j + 1]
            if a.HasProp("Count") && b.HasProp("Count") {
                if a.Count < b.Count {
                    temp := arr[j]
                    arr[j] := arr[j + 1]
                    arr[j + 1] := temp
                }
            }
        }
    }
    return arr
}
