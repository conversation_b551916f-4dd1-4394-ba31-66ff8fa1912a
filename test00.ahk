#Requires AutoHotkey v2.0
#Include UIA-v2-1.1.0\Lib\UIA.ahk

global windows := [], scanResults := []

; 测试UIA是否正常工作
try {
    testElement := UIA.GetRootElement()
    if !testElement {
        MsgBox("警告：UIA库可能未正确加载或初始化失败！")
    }
} catch as e {
    MsgBox("错误：UIA库加载失败！`n错误信息：" . e.message)
}

CreateMainGUI()

CreateMainGUI() {
    global listBox, infoText, editSearch, textStat, windows
    winGui := Gui("+Resize", "UIA 控件结构检查器")

    winGui.AddText("x10 y10 w300 h20", "选择目标窗口：")
    listBox := winGui.AddListBox("x10 y30 w300 h100 vlistBox", [])

    btnRefresh := winGui.AddButton("x320 y30 w100 h30", "刷新窗口")
    btnSimple := winGui.AddButton("x430 y30 w100 h30", "简单模式")
    btnTest := winGui.AddButton("x540 y30 w80 h30", "测试")
    btnScan := winGui.AddButton("x320 y70 w100 h30", "扫描结构")
    btnExport := winGui.AddButton("x320 y110 w100 h30", "导出结果")

    winGui.AddText("x10 y140 w300 h20", "搜索关键词（Name 或 Type）：")
    editSearch := winGui.AddEdit("x10 y160 w300 h30")
    btnSearch := winGui.AddButton("x320 y160 w100 h30", "搜索")

    winGui.AddText("x10 y200 w420 h20", "控件信息/搜索结果：")
    infoText := winGui.AddEdit("x10 y220 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnStat := winGui.AddButton("x10 y380 w200 h30", "类型统计")
    textStat := winGui.AddEdit("x10 y420 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnMonitor := winGui.AddButton("x220 y380 w100 h30", "悬停监控")
    btnStopMonitor := winGui.AddButton("x330 y380 w100 h30", "停止监控")

    btnRefresh.OnEvent("Click", RefreshWindowList)
    btnSimple.OnEvent("Click", SimpleWindowList)
    btnTest.OnEvent("Click", TestListBox)
    btnScan.OnEvent("Click", ScanUIA)
    btnExport.OnEvent("Click", ExportResult)
    btnSearch.OnEvent("Click", SearchControls)
    btnStat.OnEvent("Click", ShowControlTypeStat)
    btnMonitor.OnEvent("Click", StartMouseMonitor)
    btnStopMonitor.OnEvent("Click", StopMouseMonitor)

    RefreshWindowList()
    winGui.Show("w630 h600")
}

RefreshWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; 添加调试信息
    windowCount := 0
    addedCount := 0

    ; Get all window handles using AutoHotkey's WinGetList
    try {
        windowList := WinGetList()

        for hwnd in windowList {
            windowCount++
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                ; 检查窗口是否真实可见
                if !IsRealVisibleWindow(hwnd, title, className, processName) {
                    continue
                }

                ; 创建显示标题
                displayTitle := title
                if displayTitle == "" {
                    displayTitle := "[" . className . "] - " . processName
                }

                ; 不依赖UIA，直接添加窗口
                windows.Push({Title: displayTitle, Win: "BasicMode", Hwnd: hwnd, ClassName: className, ProcessName: processName})

                ; 添加到列表框
                try {
                    listBox.Add([displayTitle])
                    addedCount++
                } catch as e {
                    continue
                }

            } catch as e {
                continue
            }
        }
    } catch as e {
        MsgBox("获取窗口列表失败: " . e.message)
        return
    }

    ; 简化的调试信息
    MsgBox("扫描完成！`n找到 " . addedCount . " 个可见窗口")
}

; 判断是否为真实可见窗口的函数
IsRealVisibleWindow(hwnd, title, className, processName) {
    ; 基本过滤：必须有标题或者是特殊类型
    if title == "" && !IsSpecialWindowClass(className) {
        return false
    }

    ; 排除系统窗口
    if title == "Program Manager" || title == "Desktop" || title == "Task Switching" {
        return false
    }

    ; 排除隐藏窗口
    if !WinGetStyle(hwnd) & 0x10000000 {  ; WS_VISIBLE
        return false
    }

    ; 排除最小化窗口（可选）
    ; if WinGetMinMax(hwnd) == -1 {
    ;     return false
    ; }

    ; 排除无用的系统进程
    excludeProcesses := ["dwm.exe", "winlogon.exe", "csrss.exe", "wininit.exe", "services.exe", "lsass.exe", "svchost.exe"]
    for proc in excludeProcesses {
        if processName == proc {
            return false
        }
    }

    ; 排除无用的窗口类
    excludeClasses := ["Shell_TrayWnd", "DV2ControlHost", "MsgrIMEWindowClass", "SysShadow", "Button"]
    for cls in excludeClasses {
        if InStr(className, cls) {
            return false
        }
    }

    ; 排除太小的窗口（可能是隐藏窗口）
    try {
        WinGetPos(&x, &y, &w, &h, hwnd)
        if w < 50 || h < 50 {
            return false
        }
    } catch {
        return false
    }

    return true
}

; 判断是否为特殊窗口类（即使没有标题也要显示）
IsSpecialWindowClass(className) {
    specialClasses := ["Chrome_WidgetWin_1", "Notepad", "Calculator", "ApplicationFrameWindow", "CabinetWClass"]
    for cls in specialClasses {
        if InStr(className, cls) {
            return true
        }
    }
    return false
}

SimpleWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; 简单模式：不使用UIA，只获取基本窗口信息
    windowCount := 0
    addedCount := 0

    try {
        windowList := WinGetList()

        for hwnd in windowList {
            windowCount++
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                ; 简单模式：显示所有有意义的窗口
                shouldAdd := false
                displayTitle := ""

                if title != "" && title != "Program Manager" && title != "Desktop" {
                    displayTitle := title . " [简单]"
                    shouldAdd := true
                } else if className != "" && processName != "" &&
                         processName != "dwm.exe" && processName != "winlogon.exe" {
                    displayTitle := "[" . className . "] - " . processName . " [简单]"
                    shouldAdd := true
                }

                if shouldAdd && displayTitle != "" {
                    ; 不使用UIA，直接添加基本信息
                    windows.Push({Title: displayTitle, Win: "SimpleMode", Hwnd: hwnd, ClassName: className, ProcessName: processName})

                    ; 使用正确的语法添加到列表框 - 使用数组
                    try {
                        listBox.Add([displayTitle])
                        addedCount++
                    } catch {
                        ; 如果添加失败，跳过这个窗口
                        continue
                    }
                }
            } catch {
                continue
            }
        }
    } catch as e {
        MsgBox("简单模式失败: " . e.message)
        return
    }

    MsgBox("简单模式扫描完成！`n总窗口数: " . windowCount . "`n添加窗口数: " . addedCount . "`n`n注意：此模式下某些功能可能受限")
}

TestListBox(*) {
    global listBox, windows

    ; 清空并测试基本功能
    listBox.Delete()
    windows := []

    ; 添加测试项目
    testItems := ["测试项目1", "测试项目2", "记事本", "计算器", "文件资源管理器"]
    addedCount := 0

    for item in testItems {
        try {
            ; 使用正确的语法添加项目 - 使用数组
            listBox.Add([item])
            windows.Push({Title: item, Win: "TestMode", Hwnd: 0, ClassName: "Test", ProcessName: "test.exe"})
            addedCount++
        } catch as e {
            MsgBox("添加项目失败: " . item . "`n错误: " . e.message)
        }
    }

    MsgBox("测试完成！尝试添加 " . testItems.Length . " 个项目，成功添加 " . addedCount . " 个`n请检查列表框是否显示了这些项目")
}

ScanUIA(*) {
    global listBox, windows, scanResults, infoText
    selected := listBox.Value
    if !selected {
        MsgBox("请选择一个窗口")
        return
    }

    selectedWindow := windows[selected]
    hwnd := selectedWindow.Hwnd

    ; 显示基本窗口信息而不是UIA扫描
    info := "=== 窗口基本信息 ===`n"
    info .= "标题: " . selectedWindow.Title . "`n"
    info .= "类名: " . selectedWindow.ClassName . "`n"
    info .= "进程: " . selectedWindow.ProcessName . "`n"
    info .= "句柄: " . hwnd . "`n`n"

    ; 获取窗口位置和大小
    try {
        WinGetPos(&x, &y, &w, &h, hwnd)
        info .= "位置: (" . x . ", " . y . ")`n"
        info .= "大小: " . w . " x " . h . "`n`n"
    } catch {
        info .= "无法获取窗口位置信息`n`n"
    }

    ; 获取窗口状态
    try {
        minMax := WinGetMinMax(hwnd)
        state := minMax == -1 ? "最小化" : (minMax == 1 ? "最大化" : "正常")
        info .= "状态: " . state . "`n"

        if WinActive(hwnd) {
            info .= "当前窗口: 是`n"
        } else {
            info .= "当前窗口: 否`n"
        }
    } catch {
        info .= "无法获取窗口状态`n"
    }

    info .= "`n=== 说明 ===`n"
    info .= "由于UIA库问题，显示基本窗口信息。`n"
    info .= "建议使用'悬停监控'功能进行实时监控。"

    infoText.Value := info
}

TraverseElement(element, path := "") {
    global scanResults
    name := element.Name
    type := element.ControlType
    fullPath := path "/" type (name ? " [" name "]" : "")
    scanResults.Push({Path: fullPath, Type: type, Name: name, Element: element})
    for child in element.FindAllChildren() {
        TraverseElement(child, fullPath)
    }
}

ExportResult(*) {
    global scanResults
    if scanResults.Length = 0 {
        MsgBox("请先进行结构扫描")
        return
    }
    FileSelect := FileSelect("S", , "保存为...", "Text Files (*.txt)")
    if FileSelect {
        txt := ""
        for item in scanResults {
            txt .= item.Path " | " item.Type " | " item.Name "`n"
        }
        FileAppend(txt, FileSelect, "UTF-8")
        MsgBox("导出完成")
    }
}

SearchControls(*) {
    global scanResults, editSearch, infoText
    keyword := editSearch.Value
    if keyword = "" {
        MsgBox("请输入关键词")
        return
    }
    matches := ""
    for item in scanResults {
        if InStr(item.Name, keyword) || InStr(item.Type, keyword)
            matches .= item.Path " | " item.Type " | " item.Name "`n"
    }
    infoText.Value := matches != "" ? matches : "未找到匹配控件"
}

StartMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 200)
    MsgBox("鼠标悬停监控已启动（每 200ms 更新一次）`n移动鼠标到任意控件上查看信息")
}

StopMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 0)
    MsgBox("鼠标悬停监控已停止")
}

WatchUnderMouse() {
    global infoText
    try {
        ; 获取鼠标位置和窗口信息
        MouseGetPos(&x, &y, &hwnd)

        ; 显示基本窗口信息（不使用UIA）
        info := "=== 实时窗口监控 ===`n"
        info .= "鼠标位置: (" . x . ", " . y . ")`n`n"

        if hwnd {
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                info .= "=== 窗口信息 ===`n"
                info .= "标题: " . (title ? title : "[无标题]") . "`n"
                info .= "类名: " . className . "`n"
                info .= "进程: " . processName . "`n"
                info .= "句柄: " . hwnd . "`n"

                ; 获取窗口位置和大小
                try {
                    WinGetPos(&wx, &wy, &ww, &wh, hwnd)
                    info .= "窗口位置: (" . wx . ", " . wy . ")`n"
                    info .= "窗口大小: " . ww . " x " . wh . "`n"

                    ; 计算鼠标在窗口内的相对位置
                    relX := x - wx
                    relY := y - wy
                    info .= "相对位置: (" . relX . ", " . relY . ")`n"
                } catch {
                    info .= "无法获取窗口位置`n"
                }

                ; 获取窗口状态
                try {
                    minMax := WinGetMinMax(hwnd)
                    state := minMax == -1 ? "最小化" : (minMax == 1 ? "最大化" : "正常")
                    info .= "窗口状态: " . state . "`n"

                    if WinActive(hwnd) {
                        info .= "活动状态: 当前活动窗口`n"
                    } else {
                        info .= "活动状态: 非活动窗口`n"
                    }
                } catch {
                    info .= "无法获取窗口状态`n"
                }

            } catch as e {
                info .= "获取窗口信息失败: " . e.message . "`n"
            }
        } else {
            info .= "鼠标不在任何窗口上"
        }

        infoText.Value := info

    } catch as e {
        infoText.Value := "监控错误: " . e.message
    }
}

ShowControlTypeStat(*) {
    global scanResults, textStat
    stat := Map()
    for item in scanResults {
        type := item.Type
        stat[type] := stat.Has(type) ? stat[type] + 1 : 1
    }
    list := []
    for k, v in stat {
        list.Push({Type: k, Count: v})
    }
    SortArrayByCount(list)
    output := ""
    for item in list {
        output .= item.Type ": " item.Count "`n"
    }
    textStat.Value := output
}

SortArrayByCount(arr) {
    if !arr || arr.Length <= 1
        return arr
    n := arr.Length
    Loop n - 1 {
        i := A_Index
        Loop n - i {
            j := A_Index
            if (j + 1) > n
                continue
            a := arr[j]
            b := arr[j + 1]
            if a.HasProp("Count") && b.HasProp("Count") {
                if a.Count < b.Count {
                    temp := arr[j]
                    arr[j] := arr[j + 1]
                    arr[j + 1] := temp
                }
            }
        }
    }
    return arr
}
