#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 增强监控系统
=============================================================================
功能增强：
1. 深度元素分析 - 获取更详细的控件信息
2. 操作上下文分析 - 分析操作的业务逻辑
3. 脚本推断引擎 - 基于操作模式推断可能的API调用
4. 日志文件监控 - 监控CoppeliaSim的日志输出
5. 场景状态跟踪 - 跟踪仿真状态变化
=============================================================================
*/

; 全局变量
global monitoringActive := false
global coppliaElement := ""
global logFile := "Enhanced_Monitor_Log.txt"
global lastLogSize := 0
global sceneState := Map()
global operationContext := []

; 初始化增强监控系统
InitializeEnhancedMonitor() {
    global coppliaElement, logFile
    
    ; 检查 CoppeliaSim 是否运行
    if !WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("请先启动 CoppeliaSim")
        ExitApp
    }
    
    try {
        ; 获取 CoppeliaSim 窗口元素
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        coppliaElement := UIA.ElementFromHandle(hwnd)
        
        ; 初始化日志文件
        try {
            FileDelete(logFile)
        } catch {
            ; 忽略删除失败
        }
        
        FileAppend("=== CoppeliaSim 增强监控日志 ===`n", logFile, "UTF-8")
        FileAppend("监控开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile, "UTF-8")
        
        ; 初始化场景状态
        InitializeSceneState()
        
        MsgBox("增强监控系统初始化成功！`n`n热键：`nF9 - 开始/停止监控`nF10 - 显示详细分析`nF11 - 导出报告`nF12 - 退出")
        
    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 初始化场景状态
InitializeSceneState() {
    global sceneState
    
    sceneState["SimulationRunning"] := false
    sceneState["CurrentScene"] := ""
    sceneState["LoadedModels"] := []
    sceneState["LastOperation"] := ""
    sceneState["OperationCount"] := 0
}

; 开始增强监控
StartEnhancedMonitoring() {
    global monitoringActive
    
    if monitoringActive {
        return
    }
    
    monitoringActive := true
    
    ; 启动多种监控方式
    SetTimer(MonitorUIChanges, 100)           ; UI变化监控
    SetTimer(MonitorSceneState, 500)          ; 场景状态监控
    SetTimer(MonitorCoppliaLogs, 1000)        ; 日志文件监控
    SetTimer(AnalyzeOperationContext, 2000)   ; 操作上下文分析
    
    LogEnhancedOperation("系统", "监控启动", "monitor.start()", "增强监控系统已启动")
    ToolTip("增强监控已启动", 10, 10)
    SetTimer(() => ToolTip(), -2000)
}

; 停止增强监控
StopEnhancedMonitoring() {
    global monitoringActive
    
    if !monitoringActive {
        return
    }
    
    monitoringActive := false
    
    ; 停止所有定时器
    SetTimer(MonitorUIChanges, 0)
    SetTimer(MonitorSceneState, 0)
    SetTimer(MonitorCoppliaLogs, 0)
    SetTimer(AnalyzeOperationContext, 0)
    
    LogEnhancedOperation("系统", "监控停止", "monitor.stop()", "增强监控系统已停止")
    ToolTip("增强监控已停止", 10, 10)
    SetTimer(() => ToolTip(), -2000)
}

; 监控UI变化（增强版）
MonitorUIChanges() {
    global coppliaElement, monitoringActive
    
    if !monitoringActive {
        return
    }
    
    static lastClickTime := 0
    static lastElement := ""
    
    ; 检测鼠标点击
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 200) { ; 防止重复检测
            lastClickTime := currentTime
            
            ; 获取鼠标位置的元素
            MouseGetPos(&x, &y)
            try {
                element := UIA.ElementFromPoint(x, y)
                if element && element.Name {
                    elementInfo := GetDetailedElementInfo(element, x, y)
                    scriptCode := InferScriptFromOperation(element, "click")
                    
                    LogEnhancedOperation("鼠标点击", elementInfo.Name, scriptCode, elementInfo.Details)
                    
                    ; 更新操作上下文
                    UpdateOperationContext("click", elementInfo)
                }
            } catch {
                ; 忽略获取元素失败的情况
            }
        }
    }
}

; 监控场景状态
MonitorSceneState() {
    global sceneState, monitoringActive
    
    if !monitoringActive {
        return
    }
    
    try {
        ; 检查仿真状态
        windowTitle := WinGetTitle("ahk_exe coppeliasim.exe")
        isSimRunning := !InStr(windowTitle, "SIMULATION STOPPED")
        
        if isSimRunning != sceneState["SimulationRunning"] {
            sceneState["SimulationRunning"] := isSimRunning
            
            if isSimRunning {
                LogEnhancedOperation("仿真控制", "开始仿真", "sim.startSimulation()", "仿真状态：运行中")
            } else {
                LogEnhancedOperation("仿真控制", "停止仿真", "sim.stopSimulation()", "仿真状态：已停止")
            }
        }
        
        ; 检查场景变化
        if InStr(windowTitle, " - ") {
            sceneName := RegExReplace(windowTitle, ".*- (.*?) -.*", "$1")
            if sceneName != sceneState["CurrentScene"] && sceneName != "" {
                sceneState["CurrentScene"] := sceneName
                LogEnhancedOperation("场景管理", "场景切换", "sim.loadScene('" . sceneName . "')", "当前场景：" . sceneName)
            }
        }
        
    } catch {
        ; 忽略状态检查错误
    }
}

; 监控CoppeliaSim日志文件
MonitorCoppliaLogs() {
    global lastLogSize, monitoringActive
    
    if !monitoringActive {
        return
    }
    
    ; 尝试找到CoppeliaSim的日志文件
    logPaths := [
        A_AppData . "\CoppeliaSim\*",
        A_MyDocuments . "\CoppeliaSim\*",
        "C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\*"
    ]
    
    for logPath in logPaths {
        try {
            Loop Files, logPath . "*.log", "R" {
                if A_LoopFileSize > lastLogSize {
                    ; 读取新增的日志内容
                    newContent := FileRead(A_LoopFileFullPath, "UTF-8")
                    AnalyzeCoppliaLogContent(newContent)
                    lastLogSize := A_LoopFileSize
                    break
                }
            }
        } catch {
            continue
        }
    }
}

; 分析CoppeliaSim日志内容
AnalyzeCoppliaLogContent(content) {
    ; 分析日志中的关键信息
    if InStr(content, "Simulation started") {
        LogEnhancedOperation("日志分析", "仿真启动", "sim.startSimulation()", "从日志检测到仿真启动")
    }
    
    if InStr(content, "Simulation stopped") {
        LogEnhancedOperation("日志分析", "仿真停止", "sim.stopSimulation()", "从日志检测到仿真停止")
    }
    
    if InStr(content, "Loading scene") {
        scenePath := RegExReplace(content, ".*Loading scene \((.*?)\).*", "$1")
        LogEnhancedOperation("日志分析", "加载场景", "sim.loadScene('" . scenePath . "')", "场景路径：" . scenePath)
    }
    
    if InStr(content, "Loading model") {
        modelPath := RegExReplace(content, ".*Loading model \((.*?)\).*", "$1")
        LogEnhancedOperation("日志分析", "加载模型", "sim.loadModel('" . modelPath . "')", "模型路径：" . modelPath)
    }
}

; 获取详细元素信息
GetDetailedElementInfo(element, x, y) {
    elementInfo := Map()
    
    try {
        elementInfo["Name"] := element.Name ? element.Name : "[无名称]"
        elementInfo["Type"] := element.LocalizedType ? element.LocalizedType : "未知类型"
        elementInfo["ClassName"] := element.ClassName ? element.ClassName : ""
        elementInfo["Value"] := element.Value ? element.Value : ""
        elementInfo["Position"] := "(" . x . "," . y . ")"
        elementInfo["AutomationId"] := element.AutomationId ? element.AutomationId : ""
        
        ; 构建详细描述
        details := "类型:" . elementInfo["Type"]
        if elementInfo["ClassName"] != "" {
            details .= " | 类:" . elementInfo["ClassName"]
        }
        if elementInfo["AutomationId"] != "" {
            details .= " | ID:" . elementInfo["AutomationId"]
        }
        details .= " | 位置:" . elementInfo["Position"]
        
        elementInfo["Details"] := details
        
    } catch {
        elementInfo["Name"] := "[获取失败]"
        elementInfo["Details"] := "无法获取元素详细信息"
    }
    
    return elementInfo
}

; 基于操作推断脚本代码
InferScriptFromOperation(element, operation) {
    try {
        elementName := element.Name ? element.Name : ""
        elementClass := element.ClassName ? element.ClassName : ""
        
        ; 基于元素名称和类名推断API调用
        if InStr(elementName, "Start") || InStr(elementName, "开始") {
            return "sim.startSimulation()"
        } else if InStr(elementName, "Stop") || InStr(elementName, "停止") {
            return "sim.stopSimulation()"
        } else if InStr(elementName, "Pause") || InStr(elementName, "暂停") {
            return "sim.pauseSimulation()"
        } else if InStr(elementName, "File") || InStr(elementName, "文件") {
            return "sim.openScene() 或 sim.saveScene()"
        } else if InStr(elementClass, "COpenglWidget") {
            return "-- 3D视图操作：可能涉及对象选择或视角调整"
        } else if InStr(elementClass, "CModelListWidget") {
            return "-- 模型操作：可能涉及模型加载或选择"
        } else if InStr(elementClass, "CSceneHierarchyWidget") {
            return "-- 场景层次操作：可能涉及对象属性修改"
        } else {
            return "-- 可调用操作: " . elementName
        }
        
    } catch {
        return "-- 操作记录"
    }
}

; 记录增强操作日志
LogEnhancedOperation(operationType, elementName, scriptCode, details) {
    global logFile, sceneState
    
    sceneState["OperationCount"] := sceneState["OperationCount"] + 1
    
    logEntry := "时间: " . FormatTime(A_Now, "HH:mm:ss") . "`n"
    logEntry .= "操作: " . operationType . "`n"
    logEntry .= "元素: " . elementName . "`n"
    logEntry .= "脚本: " . scriptCode . "`n"
    logEntry .= "详情: " . details . "`n"
    logEntry .= "---`n"
    
    try {
        FileAppend(logEntry, logFile, "UTF-8")
    } catch {
        ; 忽略文件写入错误
    }
}

; 更新操作上下文
UpdateOperationContext(operation, elementInfo) {
    global operationContext
    
    ; 保持最近10个操作的上下文
    if operationContext.Length >= 10 {
        operationContext.RemoveAt(1)
    }
    
    ; 创建操作记录对象
    opRecord := {}
    opRecord.TimeStamp := A_Now
    opRecord.OpType := operation
    opRecord.ElInfo := elementInfo
    operationContext.Push(opRecord)
}

; 分析操作上下文
AnalyzeOperationContext() {
    global operationContext, monitoringActive
    
    if !monitoringActive || operationContext.Length < 2 {
        return
    }
    
    ; 分析操作模式
    recentOps := []
    startIndex := Max(1, operationContext.Length - 3)
    Loop operationContext.Length - startIndex + 1 {
        i := startIndex + A_Index - 1
        recentOps.Push(operationContext[i])
    }
    
    ; 检测常见操作模式
    if recentOps.Length >= 3 {
        pattern := DetectOperationPattern(recentOps)
        if pattern != "" {
            LogEnhancedOperation("模式分析", "操作模式", pattern, "检测到操作模式")
        }
    }
}

; 检测操作模式
DetectOperationPattern(operations) {
    ; 检测文件操作模式
    if operations.Length >= 2 {
        if InStr(operations[1].ElInfo["Name"], "File") && InStr(operations[2].ElInfo["Name"], "Open") {
            return "sim.openScene() -- 文件打开操作序列"
        }
        if InStr(operations[1].ElInfo["Name"], "File") && InStr(operations[2].ElInfo["Name"], "Save") {
            return "sim.saveScene() -- 文件保存操作序列"
        }
    }

    ; 检测仿真控制模式
    for op in operations {
        if InStr(op.ElInfo["Name"], "Start") {
            return "sim.startSimulation() -- 仿真启动序列"
        }
        if InStr(op.ElInfo["Name"], "Stop") {
            return "sim.stopSimulation() -- 仿真停止序列"
        }
    }
    
    return ""
}

; 热键设置
F9::ToggleEnhancedMonitoring()
F10::ShowDetailedAnalysis()
F11::ExportEnhancedReport()
F12::ExitEnhancedMonitor()

; 切换监控状态
ToggleEnhancedMonitoring() {
    global monitoringActive
    
    if !monitoringActive {
        StartEnhancedMonitoring()
    } else {
        StopEnhancedMonitoring()
    }
}

; 显示详细分析
ShowDetailedAnalysis() {
    global sceneState, operationContext
    
    analysis := "=== CoppeliaSim 详细分析 ===`n"
    analysis .= "分析时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n"
    
    analysis .= "=== 当前状态 ===`n"
    analysis .= "仿真运行: " . (sceneState["SimulationRunning"] ? "是" : "否") . "`n"
    analysis .= "当前场景: " . sceneState["CurrentScene"] . "`n"
    analysis .= "操作计数: " . sceneState["OperationCount"] . "`n`n"
    
    analysis .= "=== 最近操作 ===`n"
    startIndex := Max(1, operationContext.Length - 5)
    Loop operationContext.Length - startIndex + 1 {
        i := startIndex + A_Index - 1
        op := operationContext[i]
        analysis .= FormatTime(op.TimeStamp, "HH:mm:ss") . " - " . op.OpType . " - " . op.ElInfo["Name"] . "`n"
    }
    
    MsgBox(analysis, "详细分析")
}

; 导出增强报告
ExportEnhancedReport() {
    global sceneState, logFile
    
    reportFile := "Enhanced_Report_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
    
    try {
        report := "=== CoppeliaSim 增强监控报告 ===`n"
        report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        report .= "总操作数量: " . sceneState["OperationCount"] . "`n`n"
        
        ; 读取详细日志
        if FileExist(logFile) {
            logContent := FileRead(logFile, "UTF-8")
            report .= "=== 详细操作记录 ===`n"
            report .= logContent
        }
        
        FileAppend(report, reportFile, "UTF-8")
        MsgBox("增强报告已导出到: " . reportFile)
        
    } catch Error as e {
        MsgBox("导出失败: " . e.Message)
    }
}

; 退出增强监控
ExitEnhancedMonitor() {
    global monitoringActive
    
    if monitoringActive {
        StopEnhancedMonitoring()
    }
    
    ExitApp
}

; 初始化并启动
InitializeEnhancedMonitor()
