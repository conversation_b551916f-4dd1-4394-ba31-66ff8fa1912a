#Requires AutoHotkey v2.0
#Include UIA-v2-1.1.0\Lib\UIA.ahk

; 简单的UIA测试
try {
    MsgBox("开始UIA测试...")
    
    ; 测试1：获取根元素
    root := UIA.GetRootElement()
    if root {
        MsgBox("✓ UIA根元素获取成功")
    } else {
        MsgBox("✗ UIA根元素获取失败")
        ExitApp
    }
    
    ; 测试2：启动记事本并测试
    Run("notepad.exe")
    Sleep(2000)  ; 等待记事本启动
    
    ; 获取记事本窗口
    try {
        notepadEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        if notepadEl {
            MsgBox("✓ 记事本元素获取成功")
            
            ; 测试FindAllChildren
            children := notepadEl.FindAllChildren()
            if children {
                MsgBox("✓ FindAllChildren成功，找到 " . children.Length . " 个子控件")
            } else {
                MsgBox("✗ FindAllChildren失败")
            }
            
            ; 测试FindAllDescendants
            descendants := notepadEl.FindAllDescendants()
            if descendants {
                MsgBox("✓ FindAllDescendants成功，找到 " . descendants.Length . " 个后代控件")
            } else {
                MsgBox("✗ FindAllDescendants失败")
            }
            
        } else {
            MsgBox("✗ 记事本元素获取失败")
        }
    } catch as e {
        MsgBox("记事本测试错误: " . e.message)
    }
    
    ; 关闭记事本
    try {
        WinClose("ahk_exe notepad.exe")
    } catch {
        ; 忽略关闭错误
    }
    
} catch as e {
    MsgBox("UIA测试失败: " . e.message)
}

MsgBox("UIA测试完成")
ExitApp
